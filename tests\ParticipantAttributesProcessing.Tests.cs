using System;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Xunit;
using GenesysCloudUtils;

namespace GenesysAdapter.Tests
{
    public class ParticipantAttributesProcessingTests
    {
        private readonly ILogger<ParticipantAttributesProcessingTests> _logger;

        public ParticipantAttributesProcessingTests()
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<ParticipantAttributesProcessingTests>();
        }

        [Fact]
        public void ClearProcessedConversationTracking_ShouldAllowReprocessing()
        {
            // Arrange
            var detailData = new DetailData(_logger, null);
            
            // Create sample interaction data
            var interactionData = CreateSampleInteractionData();
            
            // Act - First call should process normally
            var result1 = detailData.GetParticipantAttributesAsync(interactionData).Result;
            
            // Clear tracking to simulate new job run
            detailData.ClearProcessedConversationTracking();
            
            // Act - Second call should also process normally (not return empty table)
            var result2 = detailData.GetParticipantAttributesAsync(interactionData).Result;
            
            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            // Both results should have the same structure (not empty due to duplicate detection)
            Assert.Equal(result1.Columns.Count, result2.Columns.Count);
        }

        [Fact]
        public void GetParticipantAttributesAsync_WithDuplicateConversations_ShouldFilterCorrectly()
        {
            // Arrange
            var detailData = new DetailData(_logger, null);
            var interactionData = CreateSampleInteractionData();
            
            // Act - First call
            var result1 = detailData.GetParticipantAttributesAsync(interactionData).Result;
            
            // Act - Second call without clearing tracking (should detect duplicates)
            var result2 = detailData.GetParticipantAttributesAsync(interactionData).Result;
            
            // Assert
            Assert.NotNull(result1);
            Assert.NotNull(result2);
            
            // Second result should be empty or have fewer rows due to duplicate filtering
            Assert.True(result2.Rows.Count <= result1.Rows.Count);
        }

        private DataTable CreateSampleInteractionData()
        {
            var table = new DataTable("InteractionData");
            table.Columns.Add("conversationid", typeof(string));
            table.Columns.Add("conversationstartdate", typeof(DateTime));
            table.Columns.Add("conversationenddate", typeof(DateTime));
            
            // Add sample data
            var row1 = table.NewRow();
            row1["conversationid"] = "conv-123";
            row1["conversationstartdate"] = DateTime.UtcNow.AddHours(-1);
            row1["conversationenddate"] = DateTime.UtcNow;
            table.Rows.Add(row1);
            
            var row2 = table.NewRow();
            row2["conversationid"] = "conv-456";
            row2["conversationstartdate"] = DateTime.UtcNow.AddHours(-2);
            row2["conversationenddate"] = DateTime.UtcNow.AddMinutes(-30);
            table.Rows.Add(row2);
            
            return table;
        }
    }
}
